<?php

namespace Modules\User\Repositories\Sentinel;

use Cartalyst\Sentinel\Laravel\Facades\Activation;
use Cartalyst\Sentinel\Laravel\Facades\Sentinel;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request; 
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Hash;
use Modules\User\Entities\Sentinel\User;
use Modules\User\Events\UserHasRegistered;
use Modules\User\Events\UserIsCreating;
use Modules\User\Events\UserIsUpdating;
use Modules\User\Events\UserWasCreated;
use Modules\User\Events\UserWasUpdated;
use Modules\User\Exceptions\UserNotFoundException;
use Modules\User\Repositories\UserRepository;
use Modules\Core\Traits\UseDataTable;
use Modules\User\Repositories\UserTokenRepository;
use Modules\User\Entities\UserProfile;

class SentinelUserRepository implements UserRepository
{
    use UseDataTable;

    /**
     * @var \Modules\User\Entities\Sentinel\User
     */
    protected $user;
    /**
     * @var \Cartalyst\Sentinel\Roles\EloquentRole
     */
    protected $role;

    public function __construct()
    {
        $this->user = Sentinel::getUserRepository()->createModel();
        $this->role = Sentinel::getRoleRepository()->createModel();
    }


    /**
     * Get option for server side index table
     * @return mixed
     */
    public function getDataTableOption()
    {
        $premuim_role_id = config("asgard.user.config.premuim_role_id");
        return [
            '_action_left'  => ['check-all'],
            '_action_right' => ['view', 'edit', '-', 'del'],
            '_extension'    => ['fixed-header'],
            '_route'        => [
                'api'     => 'api.user.user',
                'backend' => 'admin.user.user',
                'bind'    => ['id' => 'id'],
                'method'  => 'POST'
            ],
            'group'         => 'id',
            'permission'    => 'user.users',
            'search'        => ['id' => 'users.id', "school_no" => "user_profiles.school_no", "school_name" => "user_profiles.school_name", "first_name" => "users.first_name", "last_name" => "users.last_name", "email" => "users.email", "phone" => "user_profiles.phone", "province_th" => "province_th", "addr_province" => "addr_province"],
            // 'search' => ['id' => 'users.id', "school_no" => "user_profiles.school_no", "school_name" => "user_profiles.school_name", "first_name" => "users.first_name", "last_name" => "users.last_name", "email" => "users.email", "phone" => "user_profiles.phone",  "full_name" => "CONCAT(users.first_name,users.last_name)", "contact" => "CONCAT(users.email,user_profiles.phone)"],
            'columns'       => [
                $this->_action_left,
                ["data" => "id", "name" => "#", 'width' => 20],
                [
                    "data"    => "full_name",
                    "display" => function ($r) use ($premuim_role_id) {
                        $txt = "<span class='text-nowrap'>";
                        $txt .= $r->role_id == $premuim_role_id ? "<i class='fa fa-star fa-fw custom-font-warning'></i> " : "";
                        $txt .= $r->first_name . " " . $r->last_name;
                        $txt .= "</span>";
                        return $txt;
                    },
                    "name"    => _trans('user::users.form.first_name')
                ],
                [
                    "data"    => "school_name",
                    "display" => function ($r) {
                        return $r->organization;
                    },
                    "name"    => "โรงเรียน/องค์กร"
                ],
                [
                    "data"    => "province_th",
                    "display" => function ($r) {
                        return $r->province_th ?? $r->addr_province;
                    },
                    "name"    => _trans('user::users.form.province', [], 'จังหวัด')
                ],
                // ["data" => "addr_province", "name" => _trans('user::users.form.addr_province')],
                // ["data" => "email", "name" => _trans('user::users.form.email')],
                // ["data" => "phone", "name" => _trans('user::users.form.phone')],
                [
                    "data"    => "contact",
                    "display" => function ($r) {
                        $txt = "<span class='text-nowrap'><i class='fa fa-envelope fa-fw'></i> " . $r->email . "</span><br>";
                        $txt .= "<span class='text-nowrap'><i class='fa fa-phone fa-fw'></i> " . $r->phone . "</span>";
                        return $txt;
                    },
                    "name"    => "ข้อมูลติดต่อ"
                ],
                [
                    "data"    => "date",
                    "display" => function ($r) use ($premuim_role_id) {
                        $txt = "<small class='text-nowrap'>สมัครเมื่อ: " . _date_thai("d M y", $r->created_at) . "</small><br>";
                        if ($r->role_id == $premuim_role_id)
                            $txt .= "<small class='text-nowrap'>เป็นพรีเมี่ยมเมื่อ: " . _date_thai("d M y", $r->role_updated_at) . "</small><br>";
                        if ($r->expire_at)
                            $txt .= "<small class='text-nowrap'>หมดอายุเมื่อ: " . _date_thai("d M y", $r->expire_at) . "</small>";
                        return $txt;
                    },
                    "name"    => _trans('core::core.table.date'),
                    'width'   => 150
                ],
                // ["data" => "created_at", "display" => "datetime", "name" => _trans('core::core.table.created at'), 'width' => 150],
                $this->_action_right
            ],
            'class'         => function ($r) use ($premuim_role_id) {
                return $r->role_id == $premuim_role_id ? 'bg-warning' : '';
            }
        ];
    }

    /**
     * Paginating, ordering and searching through posts for server side index table
     * @param Request $request
     * @return mixed
     */
    public function serverDataTableFilteringFor(Request $request)
    {
        $premuim_role_id = config("asgard.user.config.premuim_role_id");
        $users           = $this->allWithBuilder();
        $users->select(
            'users.id',
            'users.email',
            'users.first_name',
            'users.last_name',
            'role_users.role_id',
            'user_profiles.phone',
            'user_profiles.school_no',
            'user_profiles.school_name',
            'school__schools.addr_province',
            'geo__provinces.province_th',
            'role_users.updated_at as role_updated_at',
            'role_users.expire_at',
            'users.created_at'
        )->leftjoin("role_users", "users.id", "=", "role_users.user_id")
            ->leftjoin("user_profiles", "users.id", "=", "user_profiles.user_id")
            ->leftjoin("geo__provinces", "user_profiles.province_id", "=", "geo__provinces.id")
            ->leftjoin("school__schools", "user_profiles.school_id", "=", "school__schools.id");

        if ($request->filter_user == 'premium')
            $users->where('role_id', "=", $premuim_role_id);
        else if ($request->filter_user == 'expire30d')
            $users->where('role_id', "=", $premuim_role_id)
                ->whereRaw("`expire_at` <= (CURRENT_DATE + INTERVAL 30 DAY)")
                ->whereRaw("`expire_at` > CURRENT_DATE");
        else if ($request->filter_user == 'expired')
            $users->whereNotNull('expire_at')
                ->whereRaw("`expire_at` < CURRENT_DATE");

        // ->leftjoin("school__schools", "user_profiles.school_id", "=", "school__schools.id");
        $option = $this->getDataTableOption();
        return $this->createDataTable($users, $option);
    }


    /**
     * Get option for server side index table
     * @return mixed
     */
    public function getDataTableOption_approved()
    {
        $premuim_role_id = config("asgard.user.config.premuim_role_id");
        $verify_status   = config("asgard.user.data.verify_status");
        return [
            '_action_left'  => ['check-all'],
            '_action_right' => [
                'show-custom' => function ($r) {
                    $link = route('admin.user.user.showTeacherLicenses', $r->id);
                    $has_img = "";
                    if($r->file_id)
                        $has_img = "<br> <i class='fa fa-image'></i> <small>มีรูปภาพ</small>";
                    $txt = "<a title=\"Detail\" target=\"_blank\" href=\"{$link}\" class=\"float-none d-inline-block act-click btn-row-user-show btn btn-default\" data-id=\"{$r->id}\" data-full_name=\"{$r->first_name} {$r->last_name}\" data-email=\"{$r->email}\" data-id_col=\"id\">Detail{$has_img}</a>";
                   
                    return $txt;
                }
            ],
            // '_action_right' => ['view', 'edit', '-', 'del'],
            '_extension'    => ['fixed-header'],
            '_route'        => [
                'serverSide' => 'api.user.approved.indexServerSide',
                'api'        => 'api.user.user',
                'backend'    => 'admin.user.user',
                'bind'       => ['id' => 'id']
            ],
            'group'         => 'id',
            'permission'    => 'user.users',
            'search'        => ['id' => 'users.id', "school_no" => "user_profiles.school_no", "school_name" => "user_profiles.school_name", "first_name" => "users.first_name", "last_name" => "users.last_name", "email" => "users.email", "phone" => "user_profiles.phone", "id_card" => "user_profiles.id_card"],
            // 'search' => ['id' => 'users.id',  "full_name" => "CONCAT(users.first_name,users.last_name,user_profiles.school_name)",  "contact" => "CONCAT(users.email,user_profiles.phone)"],
            'columns'       => [
                $this->_action_left,
                ["data" => "id", "name" => "#", 'width' => 20],
                [
                    "data"    => "full_name",
                    "display" => function ($r) use ($premuim_role_id) {
                        $txt = $r->role_id == $premuim_role_id ? "<i class='fa fa-star fa-fw custom-font-warning'></i> " : "";
                        $txt .= $r->full_name . "<br>";
                        $txt .= "<i class='fa fa-id-card fa-fw'></i> " . $r->id_card . "<br>";
                        $txt .= "<i class='fa fa-building fa-fw'></i> " . $r->school_name . "<br>";
                        $txt .= "<i class='fa fa-envelope fa-fw'></i> " . $r->email . "<br>";
                        $txt .= "<i class='fa fa-phone fa-fw'></i> " . $r->phone;
                        return $txt;
                    },
                    "name"    => _trans('user::users.form.first_name')
                ],
                [
                    "data"    => "tc_info",
                    "display" => function ($r) {
                        $tc_info = $r->tc_info ? json_decode($r->tc_info, 1) : [];
                        $name    = Arr::get($tc_info, "latest.name");
                        $expire  = Arr::get($tc_info, "latest.expire");
                        $txt     = "";

                        if ($name == "") {
                            $txt .= "<button class='btn btn-info btn-check-license' data-id='{$r->id}'>ตรวจสอบใบอนุญาตฯ</button>";
                        } else {
                            if ($name != $r->full_name) {
                                $txt .= "<b class='text-red'><i class='fa fa-user fa-fw'></i> " . $name . "</b><br>";
                            } else
                                $txt .= "<i class='fa fa-user fa-fw'></i> " . $name . "<br>";

                            $txt .= "<i class='fa fa-clock fa-fw'></i> Issue: " . Arr::get($tc_info, "latest.issue") . "<br>";

                            if (strtotime($expire) <= time())
                                $txt .= "<b class='text-red'><i class='fa fa-clock fa-fw'></i> Exp: " . $expire . "</b>";
                            else
                                $txt .= "<i class='fa fa-clock fa-fw'></i> Exp: " . $expire;
                        }
                        return $txt;
                    },
                    "name"    => "ข้อมูลใบอนุญาตล่าสุด"
                ],
                // ["data" => "contact", "display" => function ($r) {
                //     $txt = "<i class='fa fa-envelope fa-fw'></i> " . $r->email . "<br>";
                //     $txt .= "<i class='fa fa-phone fa-fw'></i> " . $r->phone;
                //     return $txt;
                // }, "name" => "ข้อมูลติดต่อ"],
                [
                    "data"    => "verified_at",
                    "name"    => "สถานะ",
                    "display" => function ($r) use ($verify_status) {
                        $txt = "<select class='form-control select-verify-status' data-id='{$r->id}' data-col='verified_at' data-action='verify' data-mark='__TIMESTAMP__'>";
                        foreach ($verify_status as $status) {
                            $selected = $status['value'] == $r->verify_status ? 'selected' : '';
                            $txt .= "<option {$selected} value='{$status['value']}'>";
                            $txt .= $status['label'];
                            $txt .= "</option>";
                        }
                        $txt .= "</select>";

                        if ($r->verified_at)
                            $txt .= "<small><i class='fa fa-clock fa-fw'></i> " . date("d M y H:i:s", strtotime($r->verified_at)) . "</small>";
                        
                        return $txt;
                    }
                ],
                ["data" => "created_at", "display" => "datetime", "name" => _trans('core::core.table.created at'), 'width' => 150],
                $this->_action_right
            ],
            'class'         => function ($r) {
                // return $r->verify_status == 1 ? 'bg-success' : ($r->verify_status == 2 ? 'bg-danger' : '');
                return _is_verified_pass($r) ? 'bg-success' : (_is_verified_not_pass($r) ? 'bg-danger' : '');
            }
        ];
    }

    /**
     * Paginating, ordering and searching through posts for server side index table
     * @param Request $request
     * @return mixed
     */
    public function serverDataTableFilteringFor_approved(Request $request)
    {
        $users = $this->allWithBuilder();
        $users->select(
            'users.id',
            'users.email',
            'users.pre_name',
            'users.first_name',
            'users.last_name',
            'users.verify_status',
            'users.verified_at',
            'role_users.role_id',
            'user_profiles.phone',
            'user_profiles.school_no',
            'user_profiles.school_name',
            'user_profiles.id_card',
            'user_profiles.tc_card',
            'user_profiles.tc_info',
            'user_profiles.tax_id',
            // 'm.file_id',
            'users.created_at'
        )->join("role_users", "users.id", "=", "role_users.user_id")
            ->join("user_profiles", "users.id", "=", "user_profiles.user_id")
            // ->leftjoin(DB::raw("(SELECT imageable_id, file_id FROM media__imageables WHERE zone = 'approve') m"), "users.id", "=", "m.imageable_id")
            ->whereRaw('(user_profiles.id_card IS NOT NULL OR user_profiles.tc_card IS NOT NULL OR user_profiles.tax_id IS NOT NULL)')
            ->whereRaw('FIND_IN_SET("ผู้ปกครอง", `user_profiles`.`career`) = 0 AND FIND_IN_SET("นักเรียน", `user_profiles`.`career`) = 0');

        if ($request->filter_user == 'approved')
            $users->whereNotNull('users.verified_at')->where('users.verify_status', '=', 1);
        else if ($request->filter_user == 'rejected')
            $users->whereNotNull('users.verified_at')->whereIn('users.verify_status', config('asgard.user.data.verify_no_pass'));
        else if ($request->filter_user == 'pending')
            $users->whereNull('users.verify_status');

        $option = $this->getDataTableOption_approved();
        return $this->createDataTable($users, $option);
    }

    /**
     * Get option for server side index table
     * @return mixed
     */
    public function getDataTableOption_selection()
    {
        return [
            '_action_left'  => false,
            '_action_right' => [
                'select-custom' => function ($r) {
                    return "<a title=\"Select\" class=\"float-none d-inline-block act-click btn-row-user-select btn btn-default\" data-id=\"{$r->id}\" data-full_name=\"{$r->first_name} {$r->last_name}\" data-email=\"{$r->email}\" data-id_col=\"id\">Select</a>";
                }
            ],
            // '_extension' => ['fixed-header'],
            '_route'        => [
                'api'     => 'api.user.selection',
                'backend' => 'admin.user.user',
                'bind'    => ['id' => 'id']
            ],
            'group'         => 'id',
            'search'        => ['id' => 'users.id', "school_no" => "user_profiles.school_no", "school_name" => "user_profiles.school_name", "first_name" => "users.first_name", "last_name" => "users.last_name", "email" => "users.email", "phone" => "user_profiles.phone"],
            'columns'       => [
                // $this->_action_left,
                ["data" => "id", "name" => "#", 'width' => 20],
                [
                    "data"    => "fullname",
                    "display" => function ($r) {
                        $txt = "";
                        $txt .= $r->first_name . " " . $r->last_name . " <br>";
                        $txt .= $r->email . " <br>";
                        $txt .= $r->school_no . " " . $r->school_name . " <br>";
                        return $txt;
                    },
                    "name"    => _trans('user::users.form.name')
                ],
                $this->_action_right
            ]
        ];
    }

    /**
     * Paginating, ordering and searching through posts for server side index table
     * @param Request $request
     * @return mixed
     */
    public function serverDataTableFilteringFor_selection(Request $request)
    {
        $users = $this->allWithBuilder();
        $users->select(
            'users.id',
            'users.email',
            'users.first_name',
            'users.last_name',
            'user_profiles.phone',
            'user_profiles.school_no',
            'user_profiles.school_name',
            'users.created_at'
        )->join("user_profiles", "users.id", "=", "user_id");
        $option = $this->getDataTableOption_selection();
        return $this->createDataTable($users, $option);
    }

    /**
     * Get user waiting for approve
     * @return mixed
     */
    public function getApprovePending()
    {
        $users = $this->allWithBuilder();
        $users->select(
            'users.id',
            'users.email',
            'users.first_name',
            'users.last_name',
            'users.verify_status',
            'users.verified_at',
            'user_profiles.id_card',
            'user_profiles.tc_card',
            'user_profiles.tax_id',
            'users.created_at'
        )
            ->join("user_profiles", "users.id", "=", "user_profiles.user_id")
            ->whereRaw('(user_profiles.id_card IS NOT NULL OR user_profiles.tc_card IS NOT NULL OR user_profiles.tax_id IS NOT NULL)')
            ->whereNull('users.verify_status')
            ->groupBy('users.id');

        return $users->get();
    }

    /**
     * Returns all the users
     * @return object
     */
    public function all()
    {
        return $this->user->all();
    }

    /**
     * Create a user resource
     * @param  array $data
     * @param  bool $activated
     * @return mixed
     */
    public function create(array $data, $activated = false)
    {
        $this->prepareUserData($data);

        $this->hashPassword($data);

        if($data['first_name'] == "Best")
            dd($data);

        $user = User::create($data);

        if($data['first_name'] == "Best")
            dd($data);
        
        $profile = Arr::pull($data, "profile");
        if (is_array($profile)) {
            $this->prepareUserProfileData($profile, $data, $user);
            $user->profile()->updateOrCreate(['user_id' => $user->id], $profile);
        }

        app(UserTokenRepository::class)->generateFor($user->id);

        $this->attachUserImage($data, $user);

        if ($activated) {
            $this->activateUser($user);
            event(new UserWasCreated($user));
        } else {
            event(new UserHasRegistered($user));
        }

        return $user;
    }

    /**
     * Create a user and assign roles to it
     * @param  array $data
     * @param  array $roles
     * @param bool $activated
     * @return User
     */
    public function createWithRoles($data, $roles, $activated = false)
    {
        event($event = new UserIsCreating($data));
        $user = $this->create((array) $data, $activated);

        if (!empty($roles)) {
            $this->prepareUserRolesData($roles, $data);
            $user->roles()->attach($roles);
        }

        return $user;
    }

    /**
     * Create a user and assign roles to it
     * But don't fire the user created event
     * @param array $data
     * @param array $roles
     * @param bool $activated
     * @return User
     */
    public function createWithRolesFromCli($data, $roles, $activated = false)
    {
        event($event = new UserIsCreating($data));
        $user = $this->user->create((array) $data);

        if (!empty($roles)) {
            $this->prepareUserRolesData($roles, $data);
            $user->roles()->attach($roles);
        }

        if ($activated) {
            $this->activateUser($user);
        }

        return $user;
    }

    /**
     * Find a user by its ID
     * @param $id
     * @return mixed
     */
    public function find($id)
    {
        return $this->user->find($id);
    }

    /**
     * Update a user
     * @param $user
     * @param $data
     * @return mixed
     */
    public function update($user, $data)
    {
        $this->prepareUserData($data, $user);

        $this->checkForNewPassword($data);

        $profile = Arr::pull($data, "profile");
        if (is_array($profile)) {
            $this->prepareUserProfileData($profile, $data, $user);
            $user->profile()->updateOrCreate(['user_id' => $user->id], $profile);
        }
        event($event = new UserIsUpdating($user, $data));

        $user->fill($event->getAttributes());
        $user->save();

        $this->attachUserImage($data, $user);

        event(new UserWasUpdated($user));

        return $user;
    }

    /**
     * @param $userId
     * @param $data
     * @param $roles
     * @internal param $user
     * @return mixed
     */
    public function updateAndSyncRoles($userId, $data, $roles)
    {
        $user = $this->user->find($userId);

        $this->checkForNewPassword($data);

        $this->checkForManualActivation($data, $user);

        $this->prepareUserData($data, $user);

        $profile = Arr::pull($data, "profile");
        if (is_array($profile)) {
            $this->prepareUserProfileData($profile, $data, $user);
            $user->profile()->updateOrCreate(['user_id' => $user->id], $profile);
        }

        event($event = new UserIsUpdating($user, $data));

        $user->fill($event->getAttributes());
        $user->save();

        if (!empty($roles)) {
            $this->prepareUserRolesData($roles, $data);
            $user->roles()->sync($roles);
        }
        $this->attachUserImage($data, $user);

        event(new UserWasUpdated($user));

        return $user;
    }

    /**
     * Deletes a user
     * @param $id
     * @throws UserNotFoundException
     * @return mixed
     */
    public function delete($id)
    {
        if ($user = $this->user->find($id)) {
            return $user->delete();
        }

        throw new UserNotFoundException();
    }

    /**
     * Find a user by its credentials
     * @param  array $credentials
     * @return mixed
     */
    public function findByCredentials(array $credentials)
    {
        return Sentinel::getUserRepository()->findByCredentials($credentials);
    }

    /**
     * Paginating, ordering and searching through pages for server side index table
     * @param Request $request
     * @return LengthAwarePaginator
     */
    public function serverPaginationFilteringFor(Request $request): LengthAwarePaginator
    {
        $roles = $this->allWithBuilder();

        if ($request->get('search') !== null) {
            $term = $request->get('search');
            $roles->where('first_name', 'LIKE', "%{$term}%")
                ->orWhere('last_name', 'LIKE', "%{$term}%")
                ->orWhere('email', 'LIKE', "%{$term}%")
                ->orWhere('id', $term);
        }

        if ($request->get('order_by') !== null && $request->get('order') !== 'null') {
            $order = $request->get('order') === 'ascending' ? 'asc' : 'desc';

            $roles->orderBy($request->get('order_by'), $order);
        } else {
            $roles->orderBy('created_at', 'desc');
        }

        return $roles->paginate($request->get('per_page', 10));
    }

    public function allWithBuilder(): Builder
    {
        return $this->user->newQuery();
    }

    /**
     * Hash the password key
     * @param array $data
     */
    private function hashPassword(array &$data)
    {
        $data['password'] = Hash::make($data['password']);
    }

    /**
     * Check if there is a new password given
     * If not, unset the password field
     * @param array $data
     */
    private function checkForNewPassword(array &$data)
    {
        if (array_key_exists('password', $data) === false) {
            return;
        }

        if ($data['password'] === '' || $data['password'] === null) {
            unset($data['password']);

            return;
        }

        $data['password'] = Hash::make($data['password']);
    }

    /**
     * Check and manually activate or remove activation for the user
     * @param array $data
     * @param $user
     */
    public function checkForManualActivation(array &$data, $user)
    {
        if (Activation::completed($user) && !$data['is_activated']) {
            return Activation::remove($user);
        }

        if (!Activation::completed($user) && $data['is_activated']) {
            $activation = Activation::create($user);

            return Activation::complete($user, $activation->code);
        }
    }

    /**
     * Activate a user automatically
     *
     * @param $user
     */
    private function activateUser($user)
    {
        $activation = Activation::create($user);
        Activation::complete($user, $activation->code);
    }

    /**
     * Approve a user automatically
     * @param array $data
     * @param $user
     * @return bool
     */
    public function approveUser(array &$data, $user)
    {
        if (!$data['verify_status']) {
            return $user->update(['verified_at' => null]);
        } else
            return $user->update(['verified_at' => $data['verified_at'] ?? date('Y-m-d H:i:s')]);
        // return false;
    }

    /**
     * Prepare user data before update or create 
     * @param array $data
     * @param $user
     * @return void
     */
    public function prepareUserData(array &$data, $user = null)
    {
        // $verify_status = (int) Arr::pull($data, "verify_status");
        $verify_status = (int) Arr::get($data, "verify_status");
        if ($verify_status && (!$user || ($user && !$user->verified_at)))
            $data['verified_at'] = date("Y-m-d H:i:s");
        else if (!$verify_status)
            $data['verified_at'] = null;
    }

    /**
     * Prepare user profile data before update or create 
     * @param array $profile
     * @param array $data
     * @param $user
     * @return void
     */
    public function prepareUserProfileData(array &$profile, $data = [], $user = null)
    {
        $bd = Arr::pull($data, "profile_birthday");
        if (is_array($bd))
            $profile['birthday'] = $bd['y'] . "-" . str_pad($bd['m'], 2, 0, STR_PAD_LEFT) . "-" . str_pad($bd['d'], 2, 0, STR_PAD_LEFT);

        $cs = Arr::get($profile, "career_subject");
        if (is_array($cs))
            $profile['career_subject'] = implode(",", $cs);

        $cl = Arr::get($profile, "career_level");
        if (is_array($cl))
            $profile['career_level'] = implode(",", $cl);
    }

    /**
     * Prepare user roles data before update or create 
     * @param array $roles
     * @param array $data
     * @param $user
     * @return void
     */
    public function prepareUserRolesData(array &$roles, $data = [], $user = null)
    {
        $expire_at           = null;
        $premuim_expire_time = config("asgard.user.config.premuim_expire_time");
        $premuim_role_id     = config("asgard.user.config.premuim_role_id");
        if (in_array($premuim_role_id, $roles)) {
            $expire_at = Arr::pull($data, "expire_at", date("Y-m-d H:i:s", strtotime("+" . $premuim_expire_time)));
            $newroles  = [];
            foreach ($roles as $role)
                $newroles[$role] = ['expire_at' => $expire_at];
            $roles = $newroles;
            // $roles[$premuim_role_id] =  ['expire_at' => $expire_at];
        }
    }

    /**
     * Attach user image after update or create 
     * @param array $data
     * @param $user
     * @return void
     */
    public function attachUserImage(array $data, $user)
    {
        if (request()->hasFile('res_avatar')) {
            $add_avatar = request()->file('res_avatar');
            if (is_array($add_avatar) && isset($add_avatar[0])) {
                $datafile_avatar = [
                    'user_id'  => $user->id,
                    'folder'   => 'user-avatar',
                    'zone'     => 'avatar',
                    'fileinfo' => [
                        'filename' => 'u' . $user->id . '-avatar.' . $add_avatar[0]->getClientOriginalExtension()
                    ]
                ];
            }
        }

        dispatch(new \Modules\Core\Jobs\AttachFiles($user, $add_avatar, $data['del_avatar'], $data['cur_avatar'], $datafile_avatar));
        $avatar = $user->filesByZone('avatar')->first();
        $user->update(['avatar' => $avatar ? $avatar->path->getRelativeUrl() : null]);


        if (request()->hasFile('res_approve')) {
            $add_approve = request()->file('res_approve');
            if (is_array($add_approve) && isset($add_approve[0])) {
                $datafile_approve = [
                    'user_id'  => $user->id,
                    'folder'   => 'user-approve',
                    'zone'     => 'approve',
                    'fileinfo' => [
                        'filename' => 'u' . $user->id . '-approve.' . $add_approve[0]->getClientOriginalExtension()
                    ]
                ];
            }
        }
        dispatch(new \Modules\Core\Jobs\AttachFiles($user, $add_approve, $data['del_approve'], $data['cur_approve'], $datafile_approve));
    }

    /**
     * Check teacher data on https://www.ksp.or.th/service/tepis_license.php
     * @param string $txtID
     * @param bool $redirectSSP
     * @return array|bool
     */
    public function checkTeacherLicense($txtID, $redirectSSP = false)
    {
        if (!$txtID || strlen($txtID) != 14)
            return false;
        // $txtID = _ksp_id_txt_to_array($idCard);
        return [
            'tepis' => _ksp_license_tepis($txtID, $redirectSSP)
        ];
    }
    /**
     * Check teacher data on https://www.ksp.or.th/service/license_search.php 
     * @param string $txtID
     * @param bool $redirectSSP
     * @return array|bool
     */
    public function checkTeacherLicenseMore($txtID, $redirectSSP = false)
    {
        if (!$txtID || strlen($txtID) != 13)
            return false;
        // $txtID = _ksp_id_txt_to_array($idCard);
        return [
            'regis' => _ksp_license_regis($txtID, $redirectSSP),
            'renew' => _ksp_license_renew($txtID, $redirectSSP),
            // 'teach' => _ksp_license_teach($txtID, $redirectSSP),
            // 'allow' => _ksp_allow_teach($txtID, $redirectSSP),
            // 'tepis' => _ksp_license_tepis($txtID, $redirectSSP)
        ];
    }

    /**
     * Check teacher data on https://www.ksp.or.th/service/license_search.php 
     * @param UserProfile $profile
     * @param bool $redirectSSP
     * @return array
     */
    public function checkLatestTeacherLicenses($profile, $redirectSSP = false)
    {
        $licenses = $this->checkTeacherLicenseMore($profile->id_card, $redirectSSP);

        // check with id card
        $renew = Arr::get($licenses, "renew");
        if (!is_array($renew) || (is_array($renew) && $renew['status'] == false)) {
            // the new teacher with only regis data
            $latest = Arr::last(Arr::get($licenses, "regis.data"));
        } else {
            // the old teacher with renew data
            $latest = Arr::last(Arr::get($licenses, "renew.data"));
        }
        $issue       = explode("/", str_replace("-", "/", Arr::get($latest, 4, "")));
        $expire      = explode("/", str_replace("-", "/", Arr::get($latest, 5, "")));
        $old_tc_info = $profile->tc_info ? json_decode($profile->tc_info, 1) : [];
        $new_tc_info = array_merge($old_tc_info ?? [], $licenses, [
            'latest' => [
                'issue'  => $issue[2] ? (($issue[2] - 543) . "-" . $issue[1] . "-" . $issue[0]) : "",
                'expire' => $expire[2] ? (($expire[2] - 543) . "-" . $expire[1] . "-" . $expire[0]) : "",
                'name'   => Arr::get($latest, 1)
            ]
        ]);
        $update      = ['tc_card' => Arr::get($latest, 3), 'tc_info' => json_encode($new_tc_info, JSON_UNESCAPED_UNICODE)];
        $profile->update($update);

        return $new_tc_info['latest'];
    }
}