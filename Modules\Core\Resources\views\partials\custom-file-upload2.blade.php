{{-- 
    Include blade
-- @param string $accept (.pdf, image/*, */*)
-- @param int $fixedFileCount
-- @param object $files 
-- @param string $label 
-- add  'enctype' => 'multipart/form-data' in Form::open(..)
--
    PHP
    
        $currentfiles =  request()->input('cur_files');
        $delfiles =  request()->input('del_files');
        $addfiles =  request()->file('res_files');
        $datafile = [
            'user_id' =>  auth()->id(),
            'folder' => 'training'
        ] 

        dispatch(new \Modules\Core\Jobs\AttachFiles($model, $addfiles, $delfiles, $currentfiles, $datafile));

--
--}}

@php
    // $fixedFileCount = $fixedFileCount ?? 1;
    $label = $label ?? 'เลือกไฟล์';
    $name = "{$name}" ?? 'files';
    $required = $required ?? 0;
    $maxfile = $maxfile ?? 1;
    $maxsize = $maxsize ?? 0;
@endphp

@section('custom-upload-' . $name)
    {{-- @for ($i = 0; $i < $fixedFileCount; $i++) --}}
    <div class="zd-btn-upload-wrapper zd-btn-upload-{{ $name }} {{ $files[0] ? 'has-file' : '' }} d-block">
        <button type="button" class="zd-btn-upload">
            {{ $label }}
            <input accept="{{ $accept ?? '.pdf,.xlsx,.docx' }}" name="res_{{ $name }}[]"
                id="file-upload{{ $name }}" class="input-file-upload{{ $name }}"
                {{-- id="file-upload{{ $name }}-{{ $i + 1 }}" class="input-file-upload{{ $name }}" --}}
                type="file" name="file" data-maxfile="{{ $maxfile }}" data-maxsize="{{ $maxsize }}"
                {{ $maxfile > 1 ? 'multiple' : '' }} {{ $required ? 'required' : '' }} />
        </button>

        <div class="filesbox">
            @foreach ($files as $file)
                <div class="fileitem">
                    <input class="current-file" type="hidden" name="cur_{{ $name }}[]"
                        value="{{ $file->id }}">
                    <a href="{{ $file->path }}" target="_blank" class="filename">{{ $file->filename }}</a>
                    <span class="remove-file" data-id="{{ $file->id }}">x</span>
                </div>
            @endforeach
        </div>
    </div>
    {{-- @endfor --}}
@endsection

@once
    @push('js-stack')
        <script type="text/javascript">
            var loadAndCheckFile = function(event, parent, maxfile, maxsizeKB) {
                if (!event.target.files.length)
                    return false;
                let src = URL.createObjectURL(event.target.files[0]);
                parent.addClass('has-file');
                let filesboxElem = parent.find(".filesbox");
                let invalidFile = false;

                parent.find('.current-file').each(function(e) {
                    if ($(this).val() != "") {
                        parent.append('<input type="hidden" name="del_{{ $name }}[]" value="' + $(this)
                            .val() + '">');
                        $(this).val("");
                    }
                });
                // let current = parent.find('.current-file');
                // if (current.length && current.val() != ""){
                //     parent.append('<input type="hidden" name="del_{{ $name }}[]" value="' + current.val() + '">');
                //     current.val("");
                // }

                filesboxElem.html("");
                if (event.target.files.length > 0 && event.target.files.length <= maxfile) {
                    $.each(event.target.files, function(idx) {
                        const fileURL = URL.createObjectURL(this);
                        const fileMb = this.size / 1024 ** 2;
                        const fileKB = this.size / 1024;
                        if (fileKB >= maxsizeKB && maxsizeKB != 0) {
                            let displaymaxsize = maxsizeKB < 1024 ? (maxsizeKB + " KB") :
                                ((maxsizeKB / 1024) + " MB");
                            filesboxElem.html(
                                "<div class='custom-font-red'>กรุณาเลือกไฟล์ที่ขนาดน้อยกว่า " +
                                displaymaxsize + "</div>"
                            );
                            invalidFile += 1;
                            return false;
                        } else {
                            let displaysize = fileKB < 1024 ? (fileKB.toFixed(1) + " KB") :
                                (fileMb.toFixed(1) + " MB");
                            filesboxElem.append(
                                '<br><span class="fileitem"> \
                                            <a href="' + fileURL + '" target="_blank" class="filename"> \
                                                ' + this.name + ' (' + displaysize + ') \
                                            </a> \
                                        </span>'
                            );

                            // filesboxElem.attr("href", src).append(this.name + " (" + displaysize + ")" + "<br>");
                        }
                    });
                } else if (event.target.files.length > maxfile) {
                    filesboxElem.html("<span class='custom-font-red'>อัพโหลดได้ไม่เกิน " + maxfile +
                        " ไฟล์</span>");
                    delete event.target.files;
                    invalidFile += 1;
                }
                if (invalidFile) {
                    parent.removeClass("has-file").addClass('has-error');
                } else { 
                    filesboxElem.append('<span class="remove-file"> x </span><br>');
                }
            };
        </script>
    @endpush
@endonce
@push('js-stack')
    <script type="text/javascript">
        $(document).ready(function() {

            $(document).on("click", ".zd-btn-upload-{{ $name }} .remove-file", function(e) {
                let parent = $(this).parents(".zd-btn-upload-{{ $name }}");
                let id = $(this).data("id");
                let idx = $(this).data("idx");
                if (id)
                    parent.append('<input type="hidden" name="del_{{ $name }}[]" value="' +
                        id + '">');
                if (idx)
                    parent.append('<input type="hidden" name="ext_{{ $name }}[]" value="' +
                        idx + '">');
                // parent.find('input.input-file-upload{{ $name }}').val(null);
                // parent.find('a.filename').text("").attr("href", "");

                $(this).parent().html("");
                // $(this).parent().remove();

                if (!parent.find('a').length){
                    parent.removeClass('has-file'); 
                    parent.find('input.input-file-upload{{ $name }}').val("");
                }
            });

            $(document).on("change", ".input-file-upload{{ $name }}", function(e) {
                let parent = $(this).parents(".zd-btn-upload-wrapper");
                let maxfile = $(this).data("maxfile");
                let maxsize = $(this).data("maxsize");

                loadAndCheckFile(e, parent, maxfile, maxsize);

            });
        });
    </script>
@endpush
