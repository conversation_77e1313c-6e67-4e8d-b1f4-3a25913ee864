<!-- Header Navbar: style can be found in header.less -->
<nav class="navbar navbar-static-top" role="navigation">
    <!-- Sidebar toggle button-->
    <a href="#" class="navbar-btn sidebar-toggle" data-toggle="push-menu" role="button" style="margin: 0;">
        <span class="sr-only">Toggle navigation</span>
        <i class="fa fa-bars" title="Toggle navigation"></i>
    </a>

    <div class="navbar-custom-menu">
        <ul class="nav navbar-nav">
            <?php if (is_module_enabled('Notification')): ?>
            @include('notification::partials.notifications')
            <?php endif; ?>
            <li>
                <a href="" class="publicUrl" style="display: none">
                    <i class="fa fa-eye"></i>  &nbsp; {{ trans('page::pages.view-page') }}
                </a>
            </li>
            <li>
                <a href="{{ url('/') }}">
                    <i class="fa fa-eye"></i>  &nbsp;
                        {{ trans('core::core.general.view website') }}
                </a>
            </li>
            @if(count(LaravelLocalization::getSupportedLocales())>1)
            <li class="dropdown">
                <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                    <i class="flag-icon flag-icon-{{ locale() }}"></i> &nbsp;
                    <span>
                        {{ LaravelLocalization::getCurrentLocaleName()  }}
                        <i class="caret"></i>
                    </span>
                </a>
                <ul class="dropdown-menu language-menu">
                    @foreach(LaravelLocalization::getSupportedLocales() as $localeCode => $properties)
                        <li class="{{ App::getLocale() == $localeCode ? 'active' : '' }}">
                            <a rel="alternate" lang="{{$localeCode}}" href="{{LaravelLocalization::getLocalizedURL($localeCode) }}">
                                <i class="flag-icon flag-icon-{{ $localeCode }}"></i> &nbsp; {!! $properties['native'] !!}
                            </a>
                        </li>
                    @endforeach
                </ul>
            </li>
            @endif
            <!-- User Account: style can be found in dropdown.less -->
            <li class="dropdown user user-menu">
                <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                    <i class="glyphicon glyphicon-user"></i>
                    <span>
                        <?php if (optional($currentUser)->present()->fullname() != ' '): ?>
                            {{ optional($currentUser)->present()->fullName() }}
                        <?php else: ?>
                            <em>{{trans('core::core.general.complete your profile')}}.</em>
                        <?php endif; ?>
                        <i class="caret"></i>
                    </span>
                </a>
                <ul class="dropdown-menu">
                    <!-- User image -->
                    <li class="user-header bg-light-blue">
                        <img src="{{ optional($currentUser)->present()->gravatar() }}" class="img-circle" alt="User Image" />
                        <p>
                            <?php if (optional($currentUser)->present()->fullname() != ' '): ?>
                                {{ optional($currentUser)->present()->fullname() }}
                            <?php else: ?>
                                <em>{{trans('core::core.general.complete your profile')}}.</em>
                            <?php endif; ?>
                        </p>
                    </li>
                    <!-- Menu Footer-->
                    <li class="user-footer">
                        <div class="pull-left">
                            <a href="{{ route('admin.account.profile.edit') }}" class="btn btn-default btn-flat">
                                {{ trans('core::core.general.profile') }}
                            </a>
                        </div>
                        <div class="pull-right">
                            <a href="{{ route('logout') }}" class="btn btn-danger btn-flat">
                                {{ trans('core::core.general.sign out') }}
                            </a>
                        </div>
                    </li>
                </ul>
            </li>
        </ul>
    </div>
</nav>
