@extends('layouts.account')

@section('title')
    {{ _trans('user::auth.login') }} | @parent
@stop

@include('school::partials.modal-select-school')
@section('content')
    <div class="authentication-box">
        <div class="container">
            <div class="row">
                <div class="col-md-7 p-0 card-left">
                    <div class="card tab2-card">
                        <div class="card-body">
                            <ul class="nav nav-tabs nav-material" id="top-tab" role="tablist">
                                <li class="nav-item">
                                    <a class="nav-link {{ !old('profile') && !request('regis') ? 'active' : '' }}"
                                        id="top-login-tab" data-toggle="tab" href="#top-login" role="tab"
                                        aria-controls="top-login" aria-selected="true"><span
                                            class="icon-user mr-2"></span>เข้าสู่ระบบ</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link {{ old('profile') || request('regis') ? 'active' : '' }}"
                                        id="top-register-tab" data-toggle="tab" href="#top-register" role="tab"
                                        aria-controls="top-register" aria-selected="false"><span
                                            class="icon-unlock mr-2"></span>ลงทะเบียน</a>
                                </li>
                            </ul>
                            <div class="tab-content" id="top-tabContent">
                                <div class="tab-pane fade {{ !old('profile') && !request('regis') ? 'show active' : '' }}"
                                    id="top-login" role="tabpanel" aria-labelledby="top-profile-tab">

                                    @include('partials.notifications')
                                    <form enctype="multipart/form-data" id="auth-form"
                                        class="form-validate-login form-horizontal auth-form"
                                        action="{{ _route('login.post') }}" method="post">
                                        {{ csrf_field() }}
                                        <input name="redirect_url" type="hidden" value="{{ request()->redirect_url }}">
                                        <div
                                            class="form-group has-feedback {{ $errors->has('email') ? ' has-error' : '' }}">
                                            <input type="email" class="form-control" id="email" name="email"
                                                placeholder="{{ _trans('user::auth.email') }}" value="{{ old('email') }}">
                                            <span class="glyphicon glyphicon-envelope form-control-feedback"></span>
                                            {!! $errors->first('email', '<span class="help-block">:message</span>') !!}
                                        </div>
                                        <div
                                            class="form-group has-feedback {{ $errors->has('password') ? ' has-error' : '' }}">
                                            <input type="password" class="form-control" id="password-login" name="password"
                                                placeholder="{{ _trans('user::auth.password') }}"
                                                value="{{ old('password') }}">
                                            <span class="eye-toggle">
                                                <i class="fa fa-eye-slash"></i>
                                            </span>
                                            <span class="glyphicon glyphicon-lock form-control-feedback"></span>
                                            {!! $errors->first('password', '<span class="help-block">:message</span>') !!}
                                        </div>
                                        <div class="form-terms">
                                            <div class="custom-control custom-checkbox mr-sm-2">
                                                <input type="checkbox" class="custom-control-input" name="remember_me"
                                                    checked value="1" id="customControlAutosizing">
                                                <label class="custom-control-label" for="customControlAutosizing">Remember
                                                    me</label>
                                                <a href="{{ _route('reset') }}" class="btn btn-default forgot-pass">lost
                                                    your password</a>
                                            </div>
                                        </div>
                                        <div class="form-button">
                                            <button class="btn btn-primary" type="submit">Login</button>
                                        </div>
                                        <div class="form-footer d-none">
                                            <span>Or Login up with social platforms</span>
                                            <ul class="social">
                                                {{-- @if ($result['commonContent']['setting'][61]->value == 1) --}}
                                                {{-- <li><a class="icon-google" --}}
                                                <li><a class="fab fa-google-plus-g"
                                                        href="{{ _route('login-social', ['google', 'r' => $roles]) }}"></a>
                                                </li>
                                                {{-- @endif --}}
                                                {{-- @if ($result['commonContent']['setting'][2]->value == 1) --}}
                                                {{-- <li><a class="icon-facebook" --}}
                                                <li><a class="fab fa-facebook-f"
                                                        href="{{ _route('login-social', ['facebook', 'r' => $roles]) }}"></a>
                                                </li>
                                                <li><a class="fab fa-line btn-agreement" data-label="Line"
                                                        data-href="{{ _route('login-social', ['line', 'r' => $roles]) }}"></a>
                                                </li>
                                                {{-- @endif --}}
                                                {{-- <li><a class="icon-twitter" href=""></a></li> --}}
                                                {{-- <li><a class="icon-instagram" href=""></a></li> --}}
                                                {{-- <li><a class="icon-pinterest" href=""></a></li> --}}
                                            </ul>
                                        </div>
                                    </form>
                                </div>
                                <div class="tab-pane fade {{ old('profile') || request('regis') ? 'show active' : '' }}"
                                    id="top-register" role="tabpanel" aria-labelledby="contact-top-tab">
                                    <div class="row justify-content-center">
                                        <div class="col-12 col-md-12">
                                            @include('partials.notifications')
                                            {!! Form::open(['route' => 'register.post', 'method' => 'POST', 'enctype' => 'multipart/form-data', 'id' => 'register-form']) !!}
                                            <input name="ref_id" type="hidden" value="{{ $ref_id }}">
                                            <input name="redirect_url" type="hidden"
                                                value="{{ request()->redirect_url }}">
                                            <div class="row">
                                                <div
                                                    class="form-group col-sm-12 {{ $errors->has('pre_name') ? ' has-error has-feedback' : '' }}">
                                                    <select name="pre_name" id="pre_name" class="form-control" required
                                                        placeholder="{{ _trans('user::users.form.pre_name') }} *">
                                                        <option value="">- {{ _trans('user::users.form.pre_name') }}
                                                            -
                                                        </option>
                                                        @foreach (Arr::get($selects, 'pre_name') as $p => $pre_name)
                                                            <option {{ old('pre_name') == $pre_name ? 'selected' : '' }}
                                                                value="{{ $pre_name }}">{{ $pre_name }}
                                                            </option>
                                                        @endforeach
                                                    </select>
                                                    {!! $errors->first('pre_name', '<span class="help-block">:message</span>') !!}
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div
                                                    class="form-group col-sm-6 {{ $errors->has('first_name') ? ' has-error has-feedback' : '' }}">
                                                    <input type="text" name="first_name" class="form-control" required
                                                        placeholder="{{ _trans('user::users.form.first_name') }} *"
                                                        value="{{ old('first_name') }}">
                                                    {!! $errors->first('first_name', '<span class="help-block">:message</span>') !!}
                                                </div>
                                                <div
                                                    class="form-group col-sm-6 {{ $errors->has('last_name') ? ' has-error has-feedback' : '' }}">
                                                    <input type="text" name="last_name" class="form-control" required
                                                        placeholder="{{ _trans('user::users.form.last_name') }} *"
                                                        value="{{ old('last_name') }}">
                                                    {!! $errors->first('last_name', '<span class="help-block">:message</span>') !!}
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div
                                                    class="form-group col-sm-12 {{ $errors->has('profile_birthday') ? ' has-error has-feedback' : '' }}">
                                                    {{-- <input type="text" name="profile[birthday]"
                                                    class="form-control datepicker-here digits " data-language="en"
                                                    data-date-format="yyyy-mm-dd" readonly
                                                    placeholder="{{ _trans('user::users.form.birthday') }}"
                                                    value="{{ old('profile.birthday') }}"> --}}
                                                    <div class="input-group mb-2">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"
                                                                id="basic-addon1">{{ _trans('user::users.form.birthday') }}
                                                                *</span>
                                                        </div>
                                                        <select name="profile_birthday[d]" id="profile_birthday_d"
                                                            required class="form-control">
                                                            <option value="">วัน</option>
                                                            @for ($d = 1; $d <= 31; $d++)
                                                                <option
                                                                    {{ old('profile_birthday.d') == $d ? 'selected' : '' }}
                                                                    value="{{ $d }}">{{ $d }}
                                                                </option>
                                                            @endfor
                                                        </select>
                                                        <select name="profile_birthday[m]" id="profile_birthday_m"
                                                            required class="form-control">
                                                            <option value="">เดือน</option>
                                                            @foreach (Arr::get($selects, 'months') as $m => $order_month)
                                                                <option
                                                                    {{ old('profile_birthday.m') == $m ? 'selected' : '' }}
                                                                    value="{{ $m }}">{{ $order_month }}
                                                                </option>
                                                            @endforeach
                                                        </select>
                                                        <select name="profile_birthday[y]" id="profile_birthday_y"
                                                            required class="form-control">
                                                            <option value="">ปี</option>
                                                            @for ($y = date('Y'); $y >= date('Y') - 100; $y--)
                                                                <option
                                                                    {{ old('profile_birthday.y', date('Y') + 1) == $y ? 'selected' : '' }}
                                                                    value="{{ $y }}">{{ $y + 543 }}
                                                                </option>
                                                            @endfor
                                                        </select>
                                                    </div>
                                                    {!! $errors->first('profile_birthday', '<span class="help-block">:message</span>') !!}
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div
                                                    class="form-group col-sm-6 {{ $errors->has('profile.phone') ? ' has-error has-feedback' : '' }}">
                                                    <input type="tel" name="profile[phone]" class="form-control"
                                                        required placeholder="{{ _trans('user::users.form.phone') }} *"
                                                        value="{{ old('profile.phone') }}">
                                                    {!! $errors->first('profile.phone', '<span class="help-block">:message</span>') !!}
                                                </div>

                                                @php
                                                    $default_career = config('asgard.user.config.default_career');
                                                @endphp
                                                <div
                                                    class="form-group col-sm-6 {{ $errors->has('profile.career') ? ' has-error has-feedback' : '' }}">
                                                    <select name="profile[career]" id="career" class="form-control"
                                                        required placeholder="{{ _trans('user::users.form.career') }} *">
                                                        <option value="">- {{ _trans('user::users.form.career') }} -
                                                        </option>
                                                        @foreach (Arr::get($selects, 'career') as $p => $career)
                                                            <option
                                                                {{ old('profile.career', $default_career) == $career ? 'selected' : '' }}
                                                                value="{{ $p }}">{{ $career }}
                                                            </option>
                                                        @endforeach
                                                    </select>
                                                    {!! $errors->first('profile.career', '<span class="help-block">:message</span>') !!}
                                                </div>

                                            </div>

                                            <div id="other_section"
                                                class="row {{ in_array($default_career, config('asgard.user.config.career_school_name', [])) ? ' d-none ' : '' }}">
                                                <div
                                                    class="form-group col-sm-12 {{ $errors->has('profile.organization_name') ? ' has-error has-feedback' : '' }}">
                                                    <input type="text" name="profile[organization_name]"
                                                        class="form-control" placeholder="ชื่อบริษัท/ร้านค้า/หน่วยงาน"
                                                        value="{{ old('profile.organization_name') }}">
                                                    {!! $errors->first('profile.organization_name', '<span class="help-block">:message</span>') !!}
                                                </div>
                                            </div>

                                            <div id="school_section"
                                                class="row {{ in_array($default_career, config('asgard.user.config.career_school_name', [])) ? '' : ' d-none ' }}">
                                                <div id="career_subject_section"
                                                    class="form-group col-sm-6 {{ $errors->has('profile.career_subject') ? ' has-error has-feedback' : '' }}">
                                                    <select name="profile[career_subject][]" id="career_subject"
                                                        class="form-control" multiple required style="width: 100%"
                                                        placeholder="{{ _trans('user::users.form.career_subject') }} *">
                                                        {{-- <option value="">-
                                                            {{ _trans('user::users.form.career_subject') }} -</option> --}}
                                                        @foreach (Arr::get($selects, 'career_subject') as $p => $career_subject)
                                                            <option
                                                                {{ old('profile.career_subject') == $career_subject ? 'selected' : '' }}
                                                                value="{{ $career_subject }}">{{ $career_subject }}
                                                            </option>
                                                        @endforeach
                                                    </select>
                                                    {!! $errors->first('profile.career_subject', '<span class="help-block">:message</span>') !!}
                                                </div>
                                                @php
                                                    $input_career_levels = old('profile.career_level', []);
                                                @endphp
                                                <div id="career_level_section"
                                                    class="form-group col-sm-6 {{ $errors->has('profile.career_level') ? ' has-error has-feedback' : '' }}">
                                                    <select name="profile[career_level][]" id="career_level"
                                                        class="form-control" multiple required style="width: 100%"
                                                        placeholder="{{ _trans('user::users.form.career_level') }} *">
                                                        {{-- <option value="">-
                                                            {{ _trans('user::users.form.career_level') }}
                                                            -</option> --}}
                                                        @foreach (Arr::get($selects, 'career_level') as $p => $career_level)
                                                            <option
                                                                {{ in_array($career_level, $input_career_levels) ? 'selected' : '' }}
                                                                value="{{ $career_level }}">{{ $career_level }}
                                                            </option>
                                                        @endforeach
                                                    </select>
                                                    {!! $errors->first('profile.career_level', '<span class="help-block">:message</span>') !!}
                                                </div>
                                                {{-- </div>
                                                <div class="row"> --}}


                                                <div class="col-12">
                                                    {!! Form::hidden('profile[school_id]', old('profile.school_id'), ['id' => 'o_school_id']) !!}
                                                    {!! Form::hidden('profile[school_no]', old('profile.school_no'), ['id' => 'o_school_no']) !!}
                                                    {{-- {!! Form::hidden('profile[school_region_id]', old('profile.school_region_id'), ['id' => 'o_school_region_id']) !!}
                                                    {!! Form::hidden('profile[school_province_id]', old('profile.school_province_id'), ['id' => 'o_school_province_id']) !!}
                                                    {!! Form::hidden('profile[school_district_id]', old('profile.school_district_id'), ['id' => 'o_school_district_id']) !!}
                                                    {!! Form::hidden('profile[school_subdistrict_id]', old('profile.school_subdistrict_id'), ['id' => 'o_school_subdistrict_id']) !!}
                                                    {!! Form::hidden('profile[school_postcode]', old('profile.school_postcode'), ['id' => 'o_school_postcode']) !!} --}}
                                                    {!! Form::hidden('profile[school_info]', old('profile.school_info'), ['id' => 'o_school_info']) !!}
                                                    <div
                                                        class="form-group has-feedback {{ $errors->has('profile[school_name]') ? ' has-error has-feedback' : '' }}">
                                                        {{-- {!! Form::label('school_name', 'โรงเรียน') !!} --}}
                                                        <div class="input-group">
                                                            <div class="input-group-prepend" data-toggle="modal"
                                                                data-target="#modal-school-selection">
                                                                <button type="button" class="btn bg-pink">
                                                                    <i class="fa fa-search"></i>
                                                                </button>
                                                            </div>
                                                            <!-- /btn-group -->
                                                            {!! Form::text('profile[school_name]', old('profile.school_name'), [
                                                                'required' => '',
                                                                'readonly' => '',
                                                                'data-toggle' => 'modal',
                                                                'data-target' => '#modal-school-selection',
                                                                'class' => 'form-control',
                                                                'placeholder' => _trans('user::users.form.school_name') . ' *',
                                                                'id' => 'o_school_name',
                                                            ]) !!}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-sm-12 col-12">
                                                    <div
                                                        class="form-group {{ $errors->has('profile.province_id') ? ' has-error has-feedback' : '' }}">
                                                        <select name="profile[province_id]" id="province_id"
                                                            class="form-control" required placeholder="">
                                                            <option value="">เลือกจังหวัด *</option>
                                                            @foreach (Arr::get($filters, 'province_id') as $p => $province)
                                                                <option
                                                                    {{ old('profile.province_id') == $p ? 'selected' : '' }}
                                                                    value="{{ $p }}">{{ $province }}
                                                                </option>
                                                            @endforeach
                                                        </select>
                                                        {!! $errors->first('profile.province_id', '<span class="help-block">:message</span>') !!}
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-12 pb-3">
                                                    <h4 class="profile-title">ข้อมูลยืนยันตัวตน</h4>
                                                    <small class="text-danger">* เลือกกรอกอย่างใดอย่างหนึ่ง</small>
                                                </div>
                                                <div class="col-sm-12 col-12">
                                                    <div class="form-group">
                                                        <div id="custom_radio_approve_other"
                                                            class="custom-control custom-radio-approve custom-radio {{ in_array($default_career, config('asgard.user.config.career_id_card', [])) ? '' : ' d-none ' }}">
                                                            <input class="custom-control-input radio-approve"
                                                                type="radio" id="id_card_radio" value="0"
                                                                name="profile[id_type]"
                                                                {{ !old('profile.id_type') ? 'checked' : '' }}>

                                                            <label for="id_card_radio"
                                                                class="custom-control-label">เลขบัตรประจำตัวประชาชน 13
                                                                หลัก</label>
                                                            <input type="text" name="profile[id_card]"
                                                                {{ !old('profile.id_type') ? '' : 'readonly' }}
                                                                data-for="id_card" class="form-control input-approve"
                                                                pattern="[0-9]{13}" value="{{ old('profile.id_card') }}">
                                                            {!! $errors->first('profile.id_card', '<span class="help-block">:message</span>') !!}
                                                        </div>
                                                        <div id="custom_radio_approve_teacher"
                                                            class="custom-control custom-radio-approve custom-radio {{ in_array($default_career, config('asgard.user.config.career_tc_id', [])) ? '' : ' d-none ' }}">
                                                            <input class="custom-control-input radio-approve"
                                                                type="radio" id="tc_card_radio" value="1"
                                                                name="profile[id_type]"
                                                                {{ old('profile.id_type') == 1 ? 'checked' : '' }}>

                                                            <label for="tc_card_radio"
                                                                class="custom-control-label">เลขที่ใบอนุญาตประกอบวิชาชีพฯ
                                                                14 หลัก</label>
                                                            <input type="text" name="profile[tc_card]"
                                                                {{ old('profile.id_type') == 1 ? '' : 'readonly' }}
                                                                data-for="tc_card" class="form-control input-approve"
                                                                pattern="[0-9]{14}" value="{{ old('profile.tc_card') }}">
                                                            {!! $errors->first('profile.tc_card', '<span class="help-block">:message</span>') !!}
                                                        </div>
                                                        <div id="custom_radio_approve_shop"
                                                            class="custom-control custom-radio-approve custom-radio {{ in_array($default_career, config('asgard.user.config.career_tax_id', [])) ? '' : ' d-none ' }}">
                                                            <input class="custom-control-input radio-approve"
                                                                type="radio" id="tax_id_radio" value="2"
                                                                name="profile[id_type]"
                                                                {{ old('profile.id_type') == 2 ? 'checked' : '' }}>

                                                            <label for="tax_id_radio"
                                                                class="custom-control-label">เลขประจำตัวผู้เสียภาษี 13
                                                                หลัก</label>
                                                            <input type="text" name="profile[tax_id]"
                                                                {{ old('profile.id_type') == 2 ? '' : 'readonly' }}
                                                                data-for="tax_id" class="form-control input-approve"
                                                                pattern="[0-9]{13}" value="{{ old('profile.tax_id') }}">
                                                            {!! $errors->first('profile.tax_id', '<span class="help-block">:message</span>') !!}
                                                        </div>

                                                    </div>
                                                </div>

                                                <div class="col-sm-12 col-12">
                                                    <div class='form-group text-center'>
                                                        <label id="label_approve_teacher"
                                                            class="label-approve {{ in_array($default_career, config('asgard.user.config.career_tc_id', [])) ? '' : ' d-none ' }}"
                                                            style="margin-bottom: 20px;">รูปใบอนุญาต/บัตรประจำตัวครู</label>
                                                        <label id="label_approve_shop"
                                                            class="label-approve {{ in_array($default_career, config('asgard.user.config.career_tax_id', [])) ? '' : ' d-none ' }}"
                                                            style="margin-bottom: 20px;">รูปใบอนุญาต/ใบรับรองร้านค้า</label>
                                                        <label id="label_approve_other"
                                                            class="label-approve {{ !in_array($default_career, config('asgard.user.config.career_id_card', [])) ? '' : ' d-none ' }}"
                                                            style="margin-bottom: 20px;">รูปบัตรประจำตัวประชาชน/ใบขับขี่</label>
                                                        <br>
                                                        @include('core::partials.custom-file-upload', [
                                                            'gallery' => 'approve',
                                                            'name' => 'approve',
                                                            'files' => [],
                                                        ])
                                                        @yield('custom-upload-approve')
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-12">
                                                    <div
                                                        class="form-group has-feedback {{ $errors->has('email') ? ' has-error has-feedback' : '' }}">
                                                        <input type="email" name="email" required
                                                            class="form-control"
                                                            placeholder="{{ _trans('user::auth.email') }}"
                                                            value="{{ old('email') }}">
                                                        <span
                                                            class="glyphicon glyphicon-envelope form-control-feedback"></span>
                                                        {!! $errors->first('email', '<span class="help-block">:message</span>') !!}
                                                    </div>
                                                    <div
                                                        class="form-group has-feedback {{ $errors->has('password') ? ' has-error has-feedback' : '' }}">
                                                        <input type="password" name="password" required
                                                            class="form-control" id="password"
                                                            placeholder="{{ _trans('user::auth.password') }}">
                                                        <span class="eye-toggle">
                                                            <i class="fa fa-eye-slash"></i>
                                                        </span>
                                                        <span
                                                            class="glyphicon glyphicon-lock form-control-feedback"></span>
                                                        {!! $errors->first('password', '<span class="help-block">:message</span>') !!}
                                                    </div>
                                                    <div
                                                        class="form-group has-feedback {{ $errors->has('password_confirmation') ? ' has-error has-feedback' : '' }}">
                                                        <input type="password" name="password_confirmation" required
                                                            class="form-control" id="password_confirmation"
                                                            placeholder="{{ _trans('user::auth.password confirmation') }}">
                                                        <span class="eye-toggle">
                                                            <i class="fa fa-eye-slash"></i>
                                                        </span>
                                                        <span
                                                            class="glyphicon glyphicon-log-in form-control-feedback"></span>
                                                        {!! $errors->first('password_confirmation', '<span class="help-block">:message</span>') !!}
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-12">
                                                    
                                                    <div class="g-recaptcha" data-sitekey="6LeIxxolAAAAAOZSRCKsk8DjqH1Fc4Xn3lu-61hY"></div>
                                                    {!! $errors->first('recaptcha', '<br><span class="help-block">:message</span>') !!}

                                                    <button type="button" id="btn-register"
                                                        class="btn btn-primary btn-block btn-flat">{{ _trans('user::auth.register me') }}</button>
                                                </div>
                                            </div>
                                            {!! Form::close() !!}

                                            <a href="javascript:void(0)"
                                                class="text-center btn-login">{{ _trans('user::auth.I already have a membership') }}</a>

                                            {{-- <a href="{{ route('login') }}"
                                                class="text-center">{{ _trans('user::auth.I already have a membership') }}</a> --}}
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-5 p-0 card-right">
                    <div class="card bg-primary">
                        <div class="svg-icon">
                            <img src="{{ _asset('assets/images/logo/logo0.png') }}" width="100%">
                        </div>

                        <div class="single-item">
                            <div>
                                <div>
                                    <h3>Welcome to SUKSAPANPANIT</h3>

                                    <p>Providing instructors and institutions with world-class content in the most relevant,
                                        engaging and flexible formats. </p>
                                </div>
                            </div>
                            <div>
                                <div>
                                    <h3>Macmillan</h3>

                                    <p>Through deep partnership with the world's best researchers, educators, administrators
                                        and developers,
                                        we facilitate teaching and learning opportunities that improve student engagement
                                        and success.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <a href="{{ _url('/') }}" class="btn btn-primary back-btn"><i data-feather="arrow-left"></i>back</a>
        </div>
    </div>
@stop

@push('css-stack')
    <link rel="stylesheet" type="text/css" href="{{ _asset('themes/multikart/css/date-picker.css') }}">
    <link rel="stylesheet" type="text/css"
        href="{{ _asset('modules/core/vendor/select2/4.1.0-rc.0/select2.min.css') }}">
    <style>
        .custom-upload-approve {
            width: 180px
        }

        .btn.bg-pink {
            background-color: #ff8084 !important;
            color: #fff;
            /* padding: 0!important; */
        }

        .authentication-box .container .form-group {
            position: relative;
        }

        .eye-toggle {
            display: inline-block;
            padding: 10px;
            position: absolute;
            top: 4px;
            right: 5px;
            font-size: 20px;
            color: #787878;
        }

        #top-register .eye-toggle {
            top: 0;
        }

        .swal2-popup {
            width: 36em !important;
        }

        .authentication-box .form-footer .social li a {
            color: #ff8084 !important;
            font-size: 18px;
        }

        /* .has-error.error-required  {
                    color: red;
                } */
        .has-error.error-required .select2-container,
        .has-error.error-required select,
        .has-error.error-required input {
            border: red 1px solid;
        }

        .has-error.error-required:after {
            content: "ต้องระบุข้อมูลนี้";
            color: red;
        }
        .has-error.error-confirmation:after {
            content: "การยืนยันรหัสผ่านไม่ตรงกัน";
            color: red;
        }
    </style>
@endpush
@push('js-stack')
    {{-- <script type="text/javascript"
        src="{{ _asset('modules/core/vendor/jquery.formatCurrency/jquery.formatCurrency-1.4.0.min.js') }}"></script> --}}
    <script src="{{ _asset('themes/multikart/js/datepicker/datepicker.js') }}"></script>
    <script src="{{ _asset('themes/multikart/js/datepicker/datepicker.en.js') }}"></script>
    <script src="{{ _asset('modules/core/vendor/select2/4.1.0-rc.0/select2.min.js') }}"></script>
    <script type="text/javascript">
        $(document).ready(function() {

            // $("#register-form").submit(function() {
            $(document).on("click", "#btn-register", function() {

                let isOk = 1;

                var career = $("#career").val();
                var o_school_name = $("#o_school_name").val();
                if (o_school_name == "" && (career == 'คุณครู' || career == 'นักเรียน')) {
                    $("#modal-school-selection").modal("show");
                    isOk = 0;
                    // return false;
                } else { // if(!(career == 'คุณครู' || career == 'นักเรียน')){
                    $("#o_school_name").removeAttr('required');
                }

                let elem = $("#register-form").find(
                    "input[required], input[required]:checked, select[required], textarea[required]");

                elem.each(function() {
                    if (this.value == undefined || this.value == "" || this.value == 0) {
                        if ($(this).parents('.form-group').hasClass("zd-btn-upload"))
                            $(this).parents('.box-file').addClass("has-error error-required");
                        else
                            $(this).parents('.form-group').addClass("has-error error-required")
                        isOk = 0;
                    } else {
                        if ($(this).parents('.form-group').hasClass("zd-btn-upload"))
                            $(this).parents('.box-file').removeClass("has-error error-required");
                        else
                            $(this).parents('.form-group').removeClass("has-error error-required");
                    }
                });

                var password_elem = $("#password");
                if(password_elem.val() != $("#password_confirmation").val()){
                    password_elem.parents('.form-group').addClass("has-error error-confirmation")
                    isOk = 0;
                }else{
                    password_elem.parents('.form-group').removeClass("has-error error-confirmation");
                }


                // console.log("isOk", isOk, elem.length);
                if (!isOk)
                    return false;

                $(this).attr("disabled", "");
                $("#register-form").submit();
            });

            $("#career_subject").select2({
                tags: true,
                placeholder: "{{ _trans('user::users.form.career_subject') }} *"
            });

            $("#career_level").select2({
                tags: true,
                placeholder: "{{ _trans('user::users.form.career_level') }} *"
            });

            $(document).on("click", ".btn-agreement", function() {
                var label = $(this).data("label");
                var href = $(this).data("href");
                Swal.fire({
                    icon: 'info',
                    title: 'ข้อตกลงเกี่ยวกับการใช้ข้อมูลจาก ' + label + '',
                    html: '<div class="text-left">■ <strong>ข้อมูลที่จะเก็บและนำไปใช</strong> <br>' +
                        'Email <br>' +
                        '■ <strong>วัตถุประสงค์ในการเก็บข้อมูล การใช้ และการส่งต่อให้กับบุคคลภายนอก</strong> <br>' +
                        'ข้อมูลข้างต้นจะถูกนำไปใช้สำหรับ <br>' +
                        '(1) การให้บริการตลอดจนการพัฒนาและปรับปรุงบริการต่างๆ <br>' +
                        '(2) แจ้งเตือนเมื่อเปืดจัดอบรมใหม่ <br>' +
                        '(3) แจ้งห้องอบรมที่ผู้ใช้ได้ลงทะเบียนไว้ <br>' +
                        '(4) ส่งไปรับรอง (กรณี"การอบรมที่เข้าร่วม"มีการออกใบรับรองให้)</div>',
                    // showCloseButton: true,
                    showCancelButton: true,
                    focusConfirm: false,
                    confirmButtonText: '<i class="fa fa-thumbs-up"></i> Agree!',
                    confirmButtonAriaLabel: 'Thumbs up, great!',
                    confirmButtonColor: '#ff8084',
                    cancelButtonText: 'Cancel',
                    // '<i class="fa fa-thumbs-down"></i>',
                    cancelButtonAriaLabel: 'Thumbs down'
                }).then((result) => {
                    /* Read more about isConfirmed, isDenied below */
                    if (result.isConfirmed) {
                        window.location.href = href;
                    }
                });
            });

            $(document).on("click", ".eye-toggle", function() {
                var input = $(this).parent().find("input");
                var eye = $(this).find('i');
                eye.toggleClass('fa-eye-slash').toggleClass('fa-eye');
                if (eye.hasClass("fa-eye-slash"))
                    input.attr("type", "password");
                else
                    input.attr("type", "text");
            });

            $(document).on("click", ".btn-login", function() {
                $('#top-login-tab').tab('show');
            });
            $(document).on("click", ".btn-row-school-select", function() {
                var o_school_id = $(this).data("id");
                var o_school_no = $(this).data("school_no");
                var o_school_name = $(this).data("school_name");
                var o_school_province = $(this).data("school_province");
                var o_school_district = $(this).data("school_district");
                var o_school_subdistrict = $(this).data("school_subdistrict");
                var o_school_postcode = $(this).data("school_postcode");

                // $("#o_user_id").val(o_user_id);
                $("#o_school_id").val(o_school_id);
                $("#o_school_no").val(o_school_no);
                $("#o_school_name").val(o_school_name);
                $("#o_school_province").val(o_school_province);
                $("#o_school_district").val(o_school_district);
                $("#o_school_subdistrict").val(o_school_subdistrict);
                $("#o_school_postcode").val(o_school_postcode);

                $("#modal-school-selection").modal("hide");
            });

        });
    </script>
@endpush

@include('user::partials.script-user')
