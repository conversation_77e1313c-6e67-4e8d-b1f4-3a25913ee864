<?php

use App\Imports\TempImport;
use Illuminate\Routing\Router;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Maatwebsite\Excel\Facades\Excel;
use Modules\Training\Entities\Member;
use Modules\Training\Entities\Training;
use Modules\Training\Entities\TrainingSchedule;
use Modules\User\Emails\ApproveEmail;
use Modules\User\Emails\ExpiredEmail;
use Modules\User\Emails\ExpireWarnningEmail;
use Modules\User\Entities\Sentinel\User;
use Modules\User\Http\Controllers\Api\PremiumExpirationController;
use Modules\User\Repositories\Sentinel\SentinelUserRepository;

/** @var Router $router */
$router->group(['prefix' => '/user'], function (Router $router) {

    $router->get('test5', function () {

        //         $uid = "7376
        // 4556";

        $users = User::leftjoin("role_users", "users.id", "=", "role_users.user_id")->where("id", "<>", 0)->whereNull("role_id")->get();
        // foreach($users as $u)
        //     $u->roles()->attach([2]);
        dd($users);
    });
    $router->get('test4', function () {
            $r = rand(100,900);
            $userData = [// Modules/User/Repositories/Sentinel/SentinelUserRepository.php:435
                "_token" => "Q2G8sgcz9jOumUQH76Kbvn1IFjbfMQ479XefaUqE",
                // "ref_id" => null,
                // "redirect_url" => null,
                "pre_name" => "นาง",
                "first_name" => "Best",
                "last_name" => "Senger",
                // "profile_birthday" => [
                //     "d" => "3",
                //     "m" => "11",
                //     "y" => "1977"
                // ],
                // "profile" => [
                //     "phone" => "************",
                //     "career" => "ร้านค้าตัวแทน",
                //     "organization_name" => "Estrella Ritchie",
                //     "career_subject" => [
                //     0 => "ภาษาไทย" 
                //     ],
                //     "career_level" => [
                //     0 => "อนุบาล"  
                //     ],
                //     "school_id" => null,
                //     "school_no" => null,
                //     "school_info" => null,
                //     "school_name" => null,
                //     "province_id" => "58",
                //     "id_type" => "0",
                //     "id_card" => "5515441628837",
                //     "tc_card" => null,
                //     "tax_id" => null
                // ],
                // "cur_approve" => [],
                "email" => "BEST_testdata{$r}@gmail.com",
                "password" => "BqsVc0kTDwbtDRP",
                "password_confirmation" => "BqsVc0kTDwbtDRP",
                // "g-recaptcha-response" => null,
                // "verified_at" => null
            ];
            $u = User::create($userData);
            dd($u);
    });
    $router->get('test3', function () {
        //  $u = User::find(1);
        //  return (new ApproveEmail($u))->render();
        //  return (new ExpiredEmail($u))->render();
    });
    $router->get('test2', function () {

        // $users = app(SentinelUserRepository::class)->getApprovePending();
        // $redirectSSP = true;
        // foreach ($users as $user) {
        //     $result = [
        //         'regis' => _ksp_license_regis($user->id_card, $redirectSSP),
        //         'renew' => _ksp_license_renew($user->id_card, $redirectSSP),
        //         'teach' => _ksp_license_teach($user->id_card, $redirectSSP),
        //         'allow' => _ksp_allow_teach($user->id_card, $redirectSSP),
        //         'tepis' => _ksp_license_tepis($user->tc_card, $redirectSSP)
        //     ];
        //     dd($result);
        // }
        // $id_card = "3341700215665";
        // $result = [
        //     'regis' => _ksp_license_regis($id_card, true), 
        //     'regis2' => _ksp_license_regis($id_card, false), 
        // ];
        // dd($id_card,$result);
    });
    $router->get('users', [
        'as' => 'admin.user.user.index',
        'uses' => 'UserController@index',
        'middleware' => 'can:user.users.index',
    ]);
    $router->get('users-approved', [
        'as' => 'admin.user.user.index_approved',
        'uses' => 'UserController@index_approved',
        'middleware' => 'can:user.users.index',
    ]);

    $router->get('users/create', [
        'as' => 'admin.user.user.create',
        'uses' => 'UserController@create',
        'middleware' => 'can:user.users.create',
    ]);
    $router->post('users/store', [
        'as' => 'admin.user.user.store',
        'uses' => 'UserController@store',
        'middleware' => 'can:user.users.create',
    ]);
    $router->get('users/{id}/edit', [
        'as' => 'admin.user.user.edit',
        'uses' => 'UserController@edit',
        'middleware' => 'can:user.users.edit',
    ]);
    $router->put('users/{id}/update', [
        'as' => 'admin.user.user.update',
        'uses' => 'UserController@update',
        'middleware' => 'can:user.users.edit',
    ]);
    $router->get('users/import', [
        'as' => 'admin.user.user.import',
        'uses' => 'UserController@import',
        'middleware' => 'can:user.users.create',
    ]);
    $router->post('users/store-import', [
        'as' => 'admin.user.user.store-import',
        'uses' => 'UserController@storeImport',
        'middleware' => 'can:user.users.create',
    ]);
    $router->get('users/{id}/show', [
        'as' => 'admin.user.user.show',
        'uses' => 'UserController@show',
        'middleware' => 'can:user.users.index',
    ]);
    $router->get('users/{id}/show-training-history', [
        'as' => 'admin.user.user.showTrainingHistory',
        'uses' => 'UserController@showTrainingHistory',
        'middleware' => 'can:user.users.index',
    ]);
    $router->get('users/{id}/show-teacher-licenses', [
        'as' => 'admin.user.user.showTeacherLicenses',
        'uses' => 'UserController@showTeacherLicenses',
        'middleware' => 'can:user.users.index',
    ]);
    $router->get('users/{id}/send-reset-password', [
        'as' => 'admin.user.user.sendResetPassword',
        'uses' => 'UserController@sendResetPassword',
        'middleware' => 'can:user.users.edit',
    ]);

    $router->get('users/{id}/create-api-key', [
        'as' => 'admin.user.user.createApiKey',
        'uses' => 'UserController@createApiKey',
        'middleware' => 'can:user.users.edit',
    ]);
    $router->get('users/{userTokenId}/destroy-api-key', [
        'as' => 'admin.user.user.destroyApiKey',
        'uses' => 'UserController@destroyApiKey',
        'middleware' => 'can:user.users.edit',
    ]);

    $router->match(['delete', 'get'], 'users/{id}/destroy', [
        'as' => 'admin.user.user.destroy',
        'uses' => 'UserController@destroy',
        'middleware' => 'can:user.users.destroy',
    ]);

    $router->any('users/bulk-update-role', [
        'as' => 'admin.user.user.bulk-update-role',
        'uses' => 'UserController@bulkUpdateRole',
        'middleware' => 'can:user.users.edit',
    ]);
    $router->any('users/bulk-check-api', [
        'as' => 'admin.user.user.bulk-check-api',
        'uses' => 'UserController@bulkCheckApi',
        'middleware' => 'can:user.users.edit',
    ]);
    // $router->put('premium/update', [
    //     'as' => 'admin.user.premium.update',
    //     'uses' => 'UserController@updatePremiumUsers',
    //     'middleware' => 'can:user.users.edit',
    // ]);

    $router->get('roles', [
        'as' => 'admin.user.role.index',
        'uses' => 'RolesController@index',
        'middleware' => 'can:user.roles.index',
    ]);
    $router->get('roles/create', [
        'as' => 'admin.user.role.create',
        'uses' => 'RolesController@create',
        'middleware' => 'can:user.roles.create',
    ]);
    $router->post('roles/store', [
        'as' => 'admin.user.role.store',
        'uses' => 'RolesController@store',
        'middleware' => 'can:user.roles.create',
    ]);
    $router->get('roles/{id}/edit', [
        'as' => 'admin.user.role.edit',
        'uses' => 'RolesController@edit',
        'middleware' => 'can:user.roles.edit',
    ]);
    $router->put('roles/{id}/update', [
        'as' => 'admin.user.role.update',
        'uses' => 'RolesController@update',
        'middleware' => 'can:user.roles.edit',
    ]);
    $router->match(['delete', 'get'], 'roles/{id}/destroy', [
        'as' => 'admin.user.role.destroy',
        'uses' => 'RolesController@destroy',
        'middleware' => 'can:user.roles.destroy',
    ]);

    $router->group(['prefix' => '/reports'], function (Router $router) {
        $router->get('/', [
            'as' => 'admin.user.report.index',
            'uses' => 'ReportController@index',
        ]);
    });
});


$router->group(['prefix' => '/account'], function (Router $router) {
    $router->get('profile', [
        'as' => 'admin.account.profile.edit',
        'uses' => 'Account\ProfileController@edit',
    ]);
    $router->put('profile/update', [
        'as' => 'admin.account.profile.update',
        'uses' => 'Account\ProfileController@update',
    ]);
    $router->get('api-keys', [
        'as' => 'admin.account.api.index',
        'uses' => 'Account\ApiKeysController@index',
        'middleware' => 'can:account.api-keys.index',
    ]);
    $router->get('api-keys/create', [
        'as' => 'admin.account.api.create',
        'uses' => 'Account\ApiKeysController@create',
        'middleware' => 'can:account.api-keys.create',
    ]);
    $router->match(['delete', 'get'], 'api-keys/destroy/{userTokenId}', [
        'as' => 'admin.account.api.destroy',
        'uses' => 'Account\ApiKeysController@destroy',
        'middleware' => 'can:account.api-keys.destroy',
    ]);
});
